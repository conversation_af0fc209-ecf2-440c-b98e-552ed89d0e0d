const STRAPI_URL = process.env.NEXT_PUBLIC_STRAPI_URL || "https://strapibackendproject-3x6s.onrender.com/api"
const STRAPI_TOKEN = process.env.NEXT_PUBLIC_STRAPI_TOKEN || "5848ecac1d83e40892d8c0f691aae9291f27ba65e861dc85209a9faa753df1032c44d66df909aeea39dc493442abe47e8e612b563c1487be9915499269d582ae8a9ab4bf75facb95c00ba38432e01cf568eaa39dcb3241d853d97f45394ceb4ab2dd9ca801df6cfda285e6bffb65e049c045a02cb0aee7bca76af467aa8b93ae"

type StrapiResponse<T> = {
  data: T
  meta: {
    pagination?: {
      page: number
      pageSize: number
      pageCount: number
      total: number
    }
  }
}

// Define the data structure for collection types
type StrapiCollectionResponse<T> = {
  data: Array<{
    id: number
    attributes: T
  }>
  meta: {
    pagination?: {
      page: number
      pageSize: number
      pageCount: number
      total: number
    }
  }
}

type FetchParams = {
  populate?: string
  locale?: string
  pagination?: {
    page?: number
    pageSize?: number
  }
  sort?: string
  filters?: Record<string, any>
}

export async function fetchFromStrapi<T>(
  endpoint: string,
  params?: FetchParams
): Promise<StrapiResponse<T>> {
  if (!STRAPI_TOKEN) {
    console.error("STRAPI_TOKEN is not defined")
    // Continue anyway for development, but this should be fixed
  } else {
    console.log("Using token:", STRAPI_TOKEN.substring(0, 10) + "...")
  }

  const queryParams = new URLSearchParams()

  if (params) {
    if (params.populate) queryParams.append('populate', params.populate)
    if (params.locale) queryParams.append('locale', params.locale)
    if (params.sort) queryParams.append('sort', params.sort)

    if (params.pagination) {
      if (params.pagination.page) queryParams.append('pagination[page]', params.pagination.page.toString())
      if (params.pagination.pageSize) queryParams.append('pagination[pageSize]', params.pagination.pageSize.toString())
    }

    if (params.filters) {
      Object.entries(params.filters).forEach(([key, value]) => {
        if (typeof value === 'object') {
          Object.entries(value).forEach(([operator, operatorValue]) => {
            queryParams.append(`filters[${key}][${operator}]`, operatorValue as string)
          })
        } else {
          queryParams.append(`filters[${key}]`, value as string)
        }
      })
    }
  }

  const queryString = queryParams.toString()
  const url = `${STRAPI_URL}${endpoint}${queryString ? `?${queryString}` : ""}`

  console.log(`Fetching from Strapi: ${url}`)

  try {
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    }

    if (STRAPI_TOKEN) {
      headers["Authorization"] = `Bearer ${STRAPI_TOKEN}`
    }

    console.log("Request headers:", headers)

    const response = await fetch(url, {
      headers,
      cache: 'no-store',
    })

    console.log("Response status:", response.status, response.statusText)

    if (!response.ok) {
      const errorText = await response.text()
      console.error("Error response:", errorText)
      throw new Error(`Strapi API error: ${response.status} ${response.statusText}`)
    }

    return await response.json()
  } catch (error) {
    console.error(`Error fetching from Strapi (${endpoint}):`, error)
    throw error
  }
}

// Main Sections (Single Types)
export function fetchHome(params?: FetchParams) {
  return fetchFromStrapi("/home", params)
}

export function fetchAboutKCC(params?: FetchParams) {
  return fetchFromStrapi("/about-kcc", params)
}

// Commented out as these endpoints don't exist yet in the Strapi backend
// export function fetchHistory(params?: FetchParams) {
//   return fetchFromStrapi("/history", params)
// }

// export function fetchEvents(params?: FetchParams) {
//   return fetchFromStrapi("/event", params)
// }

export async function submitRegistration(formData: any) {
  if (!STRAPI_TOKEN) {
    throw new Error("STRAPI_TOKEN is not defined")
  }

  const url = `${STRAPI_URL}/registrations`

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${STRAPI_TOKEN}`
      },
      body: JSON.stringify({
        data: {
          Name: formData.Name,
          email: formData.email,
          Number: formData.Number,
          EventName: formData.EventName,
          OrganizationName: formData.OrganizationName
        }
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Server error response:', errorText)
      throw new Error(`HTTP error! status: ${response.status}. Details: ${errorText}`)
    }

    return await response.json()
  } catch (error) {
    console.error('Error submitting registration:', error)
    throw error
  }
}

export async function submitContactForm(formData: any) {
  if (!STRAPI_TOKEN) {
    throw new Error("STRAPI_TOKEN is not defined")
  }

  const url = `${STRAPI_URL}/contacts`;

  console.log('Submitting contact form to:', url);
  console.log('Form data:', formData);

  const requestBody = {
    data: {
      Name: formData.Name,
      CompanyName: formData.CompanyName,
      email: formData.email,
      Phone: parseInt(formData.Phone) || formData.Phone,
      Theme: formData.Theme,
      Message: formData.Message
    }
  };

  console.log('Request body:', JSON.stringify(requestBody, null, 2));

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${STRAPI_TOKEN}`
      },
      body: JSON.stringify(requestBody)
    })

    console.log('Response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Server error response:', errorText)
      throw new Error(`HTTP error! status: ${response.status}. Details: ${errorText}`)
    }

    const result = await response.json()
    console.log('Contact form submitted successfully:', result);
    return result
  } catch (error) {
    console.error('Error submitting contact form:', error)
    throw error
  }
}

export function fetchContact(params?: FetchParams) {
  return fetchFromStrapi("/contact", params)
}

// About KCC Subsections (Collection Types)
export function fetchOurChambers(params?: FetchParams) {
  return fetchFromStrapi("/our-chambers", params)
}

export function fetchChamberOrgs(params?: FetchParams) {
  return fetchFromStrapi("/chamber-orgs", params)
}

export function fetchCouncilMembers(params?: FetchParams) {
  return fetchFromStrapi("/council-members", params)
}

export function fetchAffiliatedAssociations(params?: FetchParams) {
  return fetchFromStrapi("/affiliated-associations", params)
}

export function fetchVipInscriptions(params?: FetchParams) {
  return fetchFromStrapi("/vip-inscriptions", params)
}

export function fetchScholarships(params?: FetchParams) {
  return fetchFromStrapi("/scholarships", params)
}

export function fetchElderlyCenters(params?: FetchParams) {
  return fetchFromStrapi("/elderly-centers", params)
}

export function fetchKccInterviews(params?: FetchParams) {
  return fetchFromStrapi("/kcc-interviews", params)
}

export function fetchKcc75ths(params?: FetchParams) {
  return fetchFromStrapi("/kcc-75ths", params)
}

export function fetchJoinUs(params?: FetchParams) {
  return fetchFromStrapi("/join-uses", params)
}

export function fetchWhatIsKCCs(params?: FetchParams) {
  return fetchFromStrapi("/what-is-kccs", params)
}

// History Subsections (Collection Types)
export function fetchMilestones(params?: FetchParams) {
  return fetchFromStrapi("/milestones", params)
}

export function fetchListOfCouncilors(params?: FetchParams) {
  return fetchFromStrapi("/list-of-councilors", params)
}

export function fetchKeySpeeches(params?: FetchParams) {
  return fetchFromStrapi("/key-speeches", params)
}

export function fetchArchives(params?: FetchParams) {
  return fetchFromStrapi("/archives", params)
}

// Events Subsections (Collection Types)
export function fetchUpcomingEvents(params?: FetchParams) {
  return fetchFromStrapi("/upcoming-events", params)
}

export function fetchKccElites(params?: FetchParams) {
  return fetchFromStrapi("/kcc-elites", params)
}

export function fetchReceptionGuests(params?: FetchParams) {
  return fetchFromStrapi("/reception-guests", params)
}

export function fetchExchangeVisits(params?: FetchParams) {
  return fetchFromStrapi("/exchange-visits", params)
}

export function fetchCommunityCares(params?: FetchParams) {
  return fetchFromStrapi("/community-cares", params)
}

export function fetchAcademicResearches(params?: FetchParams) {
  return fetchFromStrapi("/academic-researches", params)
}

export function fetchChineseMedicineDevelopments(params?: FetchParams) {
  return fetchFromStrapi("/chinese-medicine-developments", params)
}

export function fetchNewMembers(params?: FetchParams) {
  return fetchFromStrapi("/new-members", params)
}

export function fetchForums(params?: FetchParams) {
  return fetchFromStrapi("/forums", params)
}

export function fetchExpoActivities(params?: FetchParams) {
  return fetchFromStrapi("/expo-activities", params)
}

// Add the getArticles function
export function getArticles(params?: FetchParams) {
  // This function will fetch articles from Strapi
  // If your Strapi backend doesn't have an articles endpoint yet,
  // we'll return mock data to prevent build errors
  return new Promise((resolve) => {
    resolve({
      data: [],
      meta: {
        pagination: {
          page: 1,
          pageSize: 10,
          pageCount: 0,
          total: 0
        }
      }
    });
  });
  // Once you have an articles endpoint, you can use this:
  // return fetchFromStrapi("/articles", params)
}

// Define the home data structure type
export interface HomeData {
  id: number;
  documentId: string;
  title: string;
  whatIsKCCFrame?: any[];
  managementTeamFrame?: any[];
  news?: {
    id: number;
    documentId: string;
    title: string;
    content: any[];
    date?: string;
    order?: string | number;
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
    locale: string;
    image: {
      url: string;
    } | null;
  }[];
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  locale: string;
}

// Define the What Is KCC data structure type
export interface WhatIsKCCData {
  id: number;
  documentId: string;
  title: string;
  whatiskccframe: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  locale: string;
  image: Array<{
    id: number;
    documentId: string;
    name: string;
    alternativeText: string | null;
    caption: string | null;
    width: number;
    height: number;
    formats: {
      large?: {
        ext: string;
        url: string;
        hash: string;
        mime: string;
        name: string;
        path: string | null;
        size: number;
        width: number;
        height: number;
        sizeInBytes: number;
        provider_metadata: {
          public_id: string;
          resource_type: string;
        };
      };
      medium?: {
        ext: string;
        url: string;
        hash: string;
        mime: string;
        name: string;
        path: string | null;
        size: number;
        width: number;
        height: number;
        sizeInBytes: number;
        provider_metadata: {
          public_id: string;
          resource_type: string;
        };
      };
      small?: {
        ext: string;
        url: string;
        hash: string;
        mime: string;
        name: string;
        path: string | null;
        size: number;
        width: number;
        height: number;
        sizeInBytes: number;
        provider_metadata: {
          public_id: string;
          resource_type: string;
        };
      };
      thumbnail?: {
        ext: string;
        url: string;
        hash: string;
        mime: string;
        name: string;
        path: string | null;
        size: number;
        width: number;
        height: number;
        sizeInBytes: number;
        provider_metadata: {
          public_id: string;
          resource_type: string;
        };
      };
    };
    hash: string;
    ext: string;
    mime: string;
    size: number;
    url: string;
    previewUrl: string | null;
    provider: string;
    provider_metadata: {
      public_id: string;
      resource_type: string;
    };
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
  }>;
  localizations?: Array<{
    id: number;
    documentId: string;
    title: string;
    whatiskccframe: string;
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
    locale: string;
  }>;
}

// News
export interface NewsItem {
  id: number
  documentId: string
  title: string
  content?: any
  date?: string
  order?: string
  createdAt: string
  updatedAt: string
  publishedAt: string
  locale: string
  image: Array<{
    id: number
    formats: {
      thumbnail?: { url: string }
      small?: { url: string }
      medium?: { url: string }
      large?: { url: string }
    }
    url: string
  }>
}

export function fetchNews(params?: FetchParams): Promise<StrapiResponse<NewsItem[]>> {
  return fetchFromStrapi('/news', params)
}

export async function fetchEvent(params: any = {}) {
  try {
    const queryParams = new URLSearchParams({
      ...params,
    }).toString();

    const url = `${STRAPI_URL}/event?${queryParams}`;

    const res = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
      cache: "no-store",
    });

    if (!res.ok) {
      throw new Error(`Failed to fetch event data: ${res.status}`);
    }

    return await res.json();
  } catch (error) {
    console.error("Error fetching event data:", error);
    return null;
  }
}

// Registration type definition
export interface Registration {
  id: number;
  documentId: string;
  Name: string;
  email: string;
  Number: string;
  EventName: string;
  OrganizationName: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  locale: string;
}

// Fetch user registrations by email
export async function fetchUserRegistrations(userEmail: string): Promise<Registration[]> {
  if (!STRAPI_TOKEN) {
    throw new Error("STRAPI_TOKEN is not defined");
  }

  try {
    const response = await fetchFromStrapi<Registration[]>("/registrations", {
      filters: {
        email: userEmail
      }
    });

    return response.data || [];
  } catch (error) {
    console.error('Error fetching user registrations:', error);
    throw error;
  }
}

// Suggestion Box type definition
export interface SuggestionBox {
  id: number;
  documentId: string;
  title: string;
  message: string;
  category: string;
  name: string;
  email: string;
  date: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  locale: string;
  localizations: any[];
}

// Contact form data type (matching Strapi backend structure)
export interface ContactFormData {
  Name: string;
  CompanyName: string;
  email: string;
  Phone: string | number;
  Theme: string;
  Message: string;
}

// Suggestion form data type
export interface SuggestionFormData {
  title: string;
  message: string;
  category: string;
  name: string;
  email: string;
  date: string;
}

// Fetch all suggestions
export async function fetchSuggestions(params?: FetchParams): Promise<SuggestionBox[]> {
  try {
    const response = await fetchFromStrapi<SuggestionBox[]>("/suggestion-boxes", {
      ...params,
      populate: params?.populate || "*"
    });

    return response.data || [];
  } catch (error) {
    console.error("Error fetching suggestions:", error);
    throw error;
  }
}

// Submit a new suggestion
export async function submitSuggestion(formData: SuggestionFormData, currentLang?: string) {
  if (!STRAPI_TOKEN) {
    throw new Error("STRAPI_TOKEN is not defined");
  }

  // Map language to Strapi locale
  const getLocale = (lang?: string) => {
    switch (lang) {
      case 'zh':
        return 'zh-Hant-HK'; // Traditional Chinese
      case 'cn':
        return 'zh-Hans-HK'; // Simplified Chinese
      case 'en':
      default:
        return 'en'; // English (default)
    }
  };

  const locale = getLocale(currentLang);
  const url = `${STRAPI_URL}/suggestion-boxes?locale=${locale}`;

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${STRAPI_TOKEN}`
      },
      body: JSON.stringify({
        data: formData
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Server error response:', errorText);
      throw new Error(`HTTP error! status: ${response.status}. Details: ${errorText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error submitting suggestion:', error);
    throw error;
  }
}

// Other Activities type definition
export interface OtherActivity {
  id: number;
  documentId: string;
  title: string;
  description: string;
  date: string;
  location?: string;
  participants?: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  locale: string;
  image: Array<{
    id: number;
    formats: {
      thumbnail?: { url: string };
      small?: { url: string };
      medium?: { url: string };
      large?: { url: string };
    };
    url: string;
  }>;
}

// Fetch Other Activities
export async function fetchOtherActivities(params?: FetchParams): Promise<OtherActivity[]> {
  try {
    const response = await fetchFromStrapi<OtherActivity[]>("/other-activities", {
      ...params,
      populate: params?.populate || "*"
    });

    return response.data || [];
  } catch (error) {
    console.error("Error fetching other activities:", error);
    throw error;
  }
}

// Blood Donation type definition
export interface BloodDonation {
  id: number;
  documentId: string;
  title: string;
  description: string;
  date: string;
  location?: string;
  participants?: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  locale: string;
  image: Array<{
    id: number;
    formats: {
      thumbnail?: { url: string };
      small?: { url: string };
      medium?: { url: string };
      large?: { url: string };
    };
    url: string;
  }>;
}

// Fetch Blood Donations
export async function fetchBloodDonations(params?: FetchParams): Promise<BloodDonation[]> {
  try {
    const response = await fetchFromStrapi<BloodDonation[]>("/blood-donations", {
      ...params,
      populate: params?.populate || "*"
    });

    return response.data || [];
  } catch (error) {
    console.error("Error fetching blood donations:", error);
    throw error;
  }
}

// Navy Activities type definition
export interface NavyActivity {
  id: number;
  documentId: string;
  title: string;
  description: string;
  date: string;
  location?: string;
  participants?: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  locale: string;
  image: Array<{
    id: number;
    formats: {
      thumbnail?: { url: string };
      small?: { url: string };
      medium?: { url: string };
      large?: { url: string };
    };
    url: string;
  }>;
}

// Fetch Navy Activities (Sea Scouts)
export async function fetchNavyActivities(params?: FetchParams): Promise<NavyActivity[]> {
  try {
    const response = await fetchFromStrapi<NavyActivity[]>("/navy-activities", {
      ...params,
      populate: params?.populate || "*"
    });

    return response.data || [];
  } catch (error) {
    console.error("Error fetching navy activities:", error);
    throw error;
  }
}

// KCC Development History type definition
export interface KCCDevelopmentHistory {
  id: number;
  documentId: string;
  title: string;
  description: string;
  year: string;
  location?: string;
  participants?: string;
  significance?: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  locale: string;
  image: Array<{
    id: number;
    formats: {
      thumbnail?: { url: string };
      small?: { url: string };
      medium?: { url: string };
      large?: { url: string };
    };
    url: string;
  }>;
}

// Fetch KCC Development Histories
export async function fetchKCCDevelopmentHistories(params?: FetchParams): Promise<KCCDevelopmentHistory[]> {
  try {
    const response = await fetchFromStrapi<KCCDevelopmentHistory[]>("/kcc-development-histories", {
      ...params,
      populate: params?.populate || "*"
    });

    return response.data || [];
  } catch (error) {
    console.error("Error fetching KCC development histories:", error);
    throw error;
  }
}

// Monthly Meeting type definition
export interface MonthlyMeeting {
  id: number;
  documentId: string;
  title: string;
  agenda: string;
  specialGuests?: string;
  decisionsTaken?: string;
  meetingDate: string;
  location?: string;
  participants?: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  locale: string;
  image: Array<{
    id: number;
    formats: {
      thumbnail?: { url: string };
      small?: { url: string };
      medium?: { url: string };
      large?: { url: string };
    };
    url: string;
  }>;
}

// Fetch Monthly Meetings
export async function fetchMonthlyMeetings(params?: FetchParams): Promise<MonthlyMeeting[]> {
  try {
    const response = await fetchFromStrapi<MonthlyMeeting[]>("/monthly-meetings", {
      ...params,
      populate: params?.populate || "*"
    });

    return response.data || [];
  } catch (error) {
    console.error("Error fetching monthly meetings:", error);
    throw error;
  }
}

// Forum type definition
export interface Forum {
  id: number;
  documentId: string;
  forum_title: string;
  year: string;
  ranking_or_highlights: Array<{
    type: string;
    children: Array<{
      text: string;
      type: string;
    }>;
  }>;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  locale: string;
  image: Array<{
    id: number;
    documentId: string;
    name: string;
    alternativeText: string | null;
    caption: string | null;
    width: number;
    height: number;
    formats: {
      large?: {
        ext: string;
        url: string;
        hash: string;
        mime: string;
        name: string;
        path: string | null;
        size: number;
        width: number;
        height: number;
        sizeInBytes: number;
        provider_metadata: {
          public_id: string;
          resource_type: string;
        };
      };
      medium?: {
        ext: string;
        url: string;
        hash: string;
        mime: string;
        name: string;
        path: string | null;
        size: number;
        width: number;
        height: number;
        sizeInBytes: number;
        provider_metadata: {
          public_id: string;
          resource_type: string;
        };
      };
      small?: {
        ext: string;
        url: string;
        hash: string;
        mime: string;
        name: string;
        path: string | null;
        size: number;
        width: number;
        height: number;
        sizeInBytes: number;
        provider_metadata: {
          public_id: string;
          resource_type: string;
        };
      };
      thumbnail?: {
        ext: string;
        url: string;
        hash: string;
        mime: string;
        name: string;
        path: string | null;
        size: number;
        width: number;
        height: number;
        sizeInBytes: number;
        provider_metadata: {
          public_id: string;
          resource_type: string;
        };
      };
    };
    hash: string;
    ext: string;
    mime: string;
    size: number;
    url: string;
    previewUrl: string | null;
    provider: string;
    provider_metadata: {
      public_id: string;
      resource_type: string;
    };
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
  }>;
  localizations: any[];
}

// Fetch Forums with proper typing
export async function fetchForumsData(params?: FetchParams): Promise<Forum[]> {
  try {
    const response = await fetchFromStrapi<Forum[]>("/forums", {
      ...params,
      populate: params?.populate || "*"
    });

    return response.data || [];
  } catch (error) {
    console.error("Error fetching forums:", error);
    throw error;
  }
}

// New Member type definition
export interface NewMember {
  id: number;
  documentId: string;
  title: string;
  date: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  locale: string;
  image: Array<{
    id: number;
    documentId: string;
    name: string;
    alternativeText: string | null;
    caption: string | null;
    width: number;
    height: number;
    formats: {
      large?: {
        ext: string;
        url: string;
        hash: string;
        mime: string;
        name: string;
        path: string | null;
        size: number;
        width: number;
        height: number;
        sizeInBytes: number;
        provider_metadata: {
          public_id: string;
          resource_type: string;
        };
      };
      medium?: {
        ext: string;
        url: string;
        hash: string;
        mime: string;
        name: string;
        path: string | null;
        size: number;
        width: number;
        height: number;
        sizeInBytes: number;
        provider_metadata: {
          public_id: string;
          resource_type: string;
        };
      };
      small?: {
        ext: string;
        url: string;
        hash: string;
        mime: string;
        name: string;
        path: string | null;
        size: number;
        width: number;
        height: number;
        sizeInBytes: number;
        provider_metadata: {
          public_id: string;
          resource_type: string;
        };
      };
      thumbnail?: {
        ext: string;
        url: string;
        hash: string;
        mime: string;
        name: string;
        path: string | null;
        size: number;
        width: number;
        height: number;
        sizeInBytes: number;
        provider_metadata: {
          public_id: string;
          resource_type: string;
        };
      };
    };
    hash: string;
    ext: string;
    mime: string;
    size: number;
    url: string;
    previewUrl: string | null;
    provider: string;
    provider_metadata: {
      public_id: string;
      resource_type: string;
    };
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
  }>;
  localizations: any[];
}

// Fetch New Members with proper typing
export async function fetchNewMembersData(params?: FetchParams): Promise<NewMember[]> {
  try {
    const response = await fetchFromStrapi<NewMember[]>("/new-members", {
      ...params,
      populate: params?.populate || "*"
    });

    return response.data || [];
  } catch (error) {
    console.error("Error fetching new members:", error);
    throw error;
  }
}

// Expo Activity type definition
export interface ExpoActivity {
  id: number;
  documentId: string;
  expo_name: string;
  description: string;
  date: string | null;
  location: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  locale: string;
  image: Array<{
    id: number;
    documentId: string;
    name: string;
    alternativeText: string | null;
    caption: string | null;
    width: number;
    height: number;
    formats: {
      large?: {
        ext: string;
        url: string;
        hash: string;
        mime: string;
        name: string;
        path: string | null;
        size: number;
        width: number;
        height: number;
        sizeInBytes: number;
        provider_metadata: {
          public_id: string;
          resource_type: string;
        };
      };
      medium?: {
        ext: string;
        url: string;
        hash: string;
        mime: string;
        name: string;
        path: string | null;
        size: number;
        width: number;
        height: number;
        sizeInBytes: number;
        provider_metadata: {
          public_id: string;
          resource_type: string;
        };
      };
      small?: {
        ext: string;
        url: string;
        hash: string;
        mime: string;
        name: string;
        path: string | null;
        size: number;
        width: number;
        height: number;
        sizeInBytes: number;
        provider_metadata: {
          public_id: string;
          resource_type: string;
        };
      };
      thumbnail?: {
        ext: string;
        url: string;
        hash: string;
        mime: string;
        name: string;
        path: string | null;
        size: number;
        width: number;
        height: number;
        sizeInBytes: number;
        provider_metadata: {
          public_id: string;
          resource_type: string;
        };
      };
    };
    hash: string;
    ext: string;
    mime: string;
    size: number;
    url: string;
    previewUrl: string | null;
    provider: string;
    provider_metadata: {
      public_id: string;
      resource_type: string;
    };
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
  }>;
  localizations: any[];
}

// Fetch Expo Activities with proper typing
export async function fetchExpoActivitiesData(params?: FetchParams): Promise<ExpoActivity[]> {
  try {
    const response = await fetchFromStrapi<ExpoActivity[]>("/expo-activities", {
      ...params,
      populate: params?.populate || "*"
    });

    return response.data || [];
  } catch (error) {
    console.error("Error fetching expo activities:", error);
    throw error;
  }
}

// Youth Division type definition
export interface YouthDivision {
  id: number;
  documentId: string;
  title: string;
  overview: Array<{
    type: string;
    children: Array<{
      text: string;
      type: string;
    }>;
  }>;
  members_involved: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  locale: string;
  image: Array<{
    id: number;
    documentId: string;
    name: string;
    alternativeText: string | null;
    caption: string | null;
    width: number;
    height: number;
    formats: {
      large?: {
        ext: string;
        url: string;
        hash: string;
        mime: string;
        name: string;
        path: string | null;
        size: number;
        width: number;
        height: number;
        sizeInBytes: number;
        provider_metadata: {
          public_id: string;
          resource_type: string;
        };
      };
      medium?: {
        ext: string;
        url: string;
        hash: string;
        mime: string;
        name: string;
        path: string | null;
        size: number;
        width: number;
        height: number;
        sizeInBytes: number;
        provider_metadata: {
          public_id: string;
          resource_type: string;
        };
      };
      small?: {
        ext: string;
        url: string;
        hash: string;
        mime: string;
        name: string;
        path: string | null;
        size: number;
        width: number;
        height: number;
        sizeInBytes: number;
        provider_metadata: {
          public_id: string;
          resource_type: string;
        };
      };
      thumbnail?: {
        ext: string;
        url: string;
        hash: string;
        mime: string;
        name: string;
        path: string | null;
        size: number;
        width: number;
        height: number;
        sizeInBytes: number;
        provider_metadata: {
          public_id: string;
          resource_type: string;
        };
      };
    };
    hash: string;
    ext: string;
    mime: string;
    size: number;
    url: string;
    previewUrl: string | null;
    provider: string;
    provider_metadata: {
      public_id: string;
      resource_type: string;
    };
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
  }>;
  localizations: any[];
}

// Fetch Youth Divisions with proper typing
export async function fetchYouthDivisionsData(params?: FetchParams): Promise<YouthDivision[]> {
  try {
    const response = await fetchFromStrapi<YouthDivision[]>("/youth-divisions", {
      ...params,
      populate: params?.populate || "*"
    });

    return response.data || [];
  } catch (error) {
    console.error("Error fetching youth divisions:", error);
    throw error;
  }
}

// Elderly Activity type definition
export interface ElderlyActivity {
  id: number;
  documentId: string;
  activitytitle: string;
  description: string;
  date: string;
  location: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  locale: string;
  image: Array<{
    id: number;
    documentId: string;
    name: string;
    alternativeText: string | null;
    caption: string | null;
    width: number;
    height: number;
    formats: {
      large?: {
        ext: string;
        url: string;
        hash: string;
        mime: string;
        name: string;
        path: string | null;
        size: number;
        width: number;
        height: number;
        sizeInBytes: number;
        provider_metadata: {
          public_id: string;
          resource_type: string;
        };
      };
      medium?: {
        ext: string;
        url: string;
        hash: string;
        mime: string;
        name: string;
        path: string | null;
        size: number;
        width: number;
        height: number;
        sizeInBytes: number;
        provider_metadata: {
          public_id: string;
          resource_type: string;
        };
      };
      small?: {
        ext: string;
        url: string;
        hash: string;
        mime: string;
        name: string;
        path: string | null;
        size: number;
        width: number;
        height: number;
        sizeInBytes: number;
        provider_metadata: {
          public_id: string;
          resource_type: string;
        };
      };
      thumbnail?: {
        ext: string;
        url: string;
        hash: string;
        mime: string;
        name: string;
        path: string | null;
        size: number;
        width: number;
        height: number;
        sizeInBytes: number;
        provider_metadata: {
          public_id: string;
          resource_type: string;
        };
      };
    };
    hash: string;
    ext: string;
    mime: string;
    size: number;
    url: string;
    previewUrl: string | null;
    provider: string;
    provider_metadata: {
      public_id: string;
      resource_type: string;
    };
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
  }>;
  localizations?: any[];
}

// Fetch Elderly Activities with proper typing
export async function fetchElderlyActivities(params?: FetchParams): Promise<ElderlyActivity[]> {
  try {
    const response = await fetchFromStrapi<ElderlyActivity[]>("/elderly-activities", {
      ...params,
      populate: params?.populate || "*"
    });

    return response.data || [];
  } catch (error) {
    console.error("Error fetching elderly activities:", error);
    throw error;
  }
}

